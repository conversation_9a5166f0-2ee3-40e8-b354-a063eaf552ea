/**
 * 地形物理设置工具组件
 * 用于设置地形物理属性
 */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Form,
  InputNumber,
  Button,
  Select,
  Slider,
  Switch,
  Divider,
  Card,
  Row,
  Col,
  Typography,
  message
} from 'antd';
import {
  SaveOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import './TerrainPhysicsSettingsTool.less';

const { Option } = Select;
const { Text } = Typography;

/**
 * 地形物理材质类型
 */
enum TerrainPhysicsMaterialType {
  /** 默认 */
  DEFAULT = 'default',
  /** 岩石 */
  ROCK = 'rock',
  /** 土壤 */
  SOIL = 'soil',
  /** 沙子 */
  SAND = 'sand',
  /** 冰 */
  ICE = 'ice',
  /** 木头 */
  WOOD = 'wood',
  /** 金属 */
  METAL = 'metal',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 地形物理设置工具属性
 */
interface TerrainPhysicsSettingsToolProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 地形修改回调 */
  onTerrainModified?: () => void;
  /** 操作回调 */
  onOperation?: (operation: any) => void;
}

/**
 * 地形物理设置工具组件
 */
const TerrainPhysicsSettingsTool: React.FC<TerrainPhysicsSettingsToolProps> = ({
  entityId,
  editable = true,
  onTerrainModified,
  onOperation
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取地形数据
  const terrainData = useSelector((state: RootState) => {
    if (!entityId) return null;
    const entity = state.scene.entities.find(e => e.id === entityId);
    return entity?.components?.TerrainComponent || null;
  });

  // 状态
  const [usePhysics, setUsePhysics] = useState<boolean>(false);
  const [physicsResolution, setPhysicsResolution] = useState<number>(64);
  const [materialType, setMaterialType] = useState<TerrainPhysicsMaterialType>(TerrainPhysicsMaterialType.DEFAULT);
  const [friction, setFriction] = useState<number>(0.5);
  const [restitution, setRestitution] = useState<number>(0.1);
  const [density, setDensity] = useState<number>(1.0);
  const [showDebugVisuals, setShowDebugVisuals] = useState<boolean>(false);

  // 初始化状态
  useEffect(() => {
    if (terrainData) {
      setUsePhysics(terrainData.usePhysics || false);
      setPhysicsResolution(terrainData.physicsResolution || 64);
      setMaterialType(terrainData.physicsMaterialType || TerrainPhysicsMaterialType.DEFAULT);
      setFriction(terrainData.physicsFriction ?? 0.5);
      setRestitution(terrainData.physicsRestitution ?? 0.1);
      setDensity(terrainData.physicsDensity ?? 1.0);
      setShowDebugVisuals(terrainData.showPhysicsDebug || false);
    }
  }, [terrainData]);

  // 处理使用物理变更
  const handleUsePhysicsChange = useCallback((checked: boolean) => {
    setUsePhysics(checked);
  }, []);

  // 处理物理分辨率变更
  const handlePhysicsResolutionChange = useCallback((value: number) => {
    setPhysicsResolution(value);
  }, []);

  // 处理材质类型变更
  const handleMaterialTypeChange = (value: TerrainPhysicsMaterialType) => {
    setMaterialType(value);

    // 根据材质类型设置默认值
    switch (value) {
      case TerrainPhysicsMaterialType.ROCK:
        setFriction(0.8);
        setRestitution(0.2);
        setDensity(2.5);
        break;
      case TerrainPhysicsMaterialType.SOIL:
        setFriction(0.6);
        setRestitution(0.1);
        setDensity(1.5);
        break;
      case TerrainPhysicsMaterialType.SAND:
        setFriction(0.5);
        setRestitution(0.05);
        setDensity(1.6);
        break;
      case TerrainPhysicsMaterialType.ICE:
        setFriction(0.1);
        setRestitution(0.3);
        setDensity(0.9);
        break;
      case TerrainPhysicsMaterialType.WOOD:
        setFriction(0.4);
        setRestitution(0.2);
        setDensity(0.7);
        break;
      case TerrainPhysicsMaterialType.METAL:
        setFriction(0.3);
        setRestitution(0.5);
        setDensity(7.8);
        break;
      case TerrainPhysicsMaterialType.DEFAULT:
      default:
        setFriction(0.5);
        setRestitution(0.1);
        setDensity(1.0);
        break;
    }
  };

  // 处理摩擦力变更
  const handleFrictionChange = useCallback((value: number) => {
    setFriction(value);
    if (materialType !== TerrainPhysicsMaterialType.CUSTOM) {
      setMaterialType(TerrainPhysicsMaterialType.CUSTOM);
    }
  }, [materialType]);

  // 处理弹性变更
  const handleRestitutionChange = useCallback((value: number) => {
    setRestitution(value);
    if (materialType !== TerrainPhysicsMaterialType.CUSTOM) {
      setMaterialType(TerrainPhysicsMaterialType.CUSTOM);
    }
  }, [materialType]);

  // 处理密度变更
  const handleDensityChange = useCallback((value: number) => {
    setDensity(value);
    if (materialType !== TerrainPhysicsMaterialType.CUSTOM) {
      setMaterialType(TerrainPhysicsMaterialType.CUSTOM);
    }
  }, [materialType]);

  // 处理显示调试可视化变更
  const handleShowDebugVisualsChange = useCallback((checked: boolean) => {
    setShowDebugVisuals(checked);
  }, []);

  // 处理应用设置
  const handleApplySettings = useCallback(() => {
    if (!entityId || !terrainData) {
      message.error(t('terrain.errors.noTerrainSelected'));
      return;
    }

    // 更新地形物理设置
    dispatch({
      type: 'UPDATE_TERRAIN_COMPONENT',
      payload: {
        entityId,
        component: 'TerrainComponent',
        properties: {
          usePhysics,
          physicsResolution,
          physicsMaterialType: materialType,
          physicsFriction: friction,
          physicsRestitution: restitution,
          physicsDensity: density,
          showPhysicsDebug: showDebugVisuals,
          needsPhysicsUpdate: true
        }
      }
    });

    // 通知地形修改
    if (onTerrainModified) {
      onTerrainModified();
    }

    // 记录操作
    if (onOperation) {
      onOperation({
        type: 'UPDATE_TERRAIN_PHYSICS',
        usePhysics,
        physicsResolution,
        materialType,
        friction,
        restitution,
        density,
        showDebugVisuals
      });
    }

    message.success(t('terrain.physics.settingsApplied'));
  }, [
    entityId,
    terrainData,
    t,
    dispatch,
    usePhysics,
    physicsResolution,
    materialType,
    friction,
    restitution,
    density,
    showDebugVisuals,
    onTerrainModified,
    onOperation
  ]);

  // 获取材质类型选项
  const materialTypeOptions = useMemo(() => [
    { value: TerrainPhysicsMaterialType.DEFAULT, label: t('terrain.physics.materials.default') },
    { value: TerrainPhysicsMaterialType.ROCK, label: t('terrain.physics.materials.rock') },
    { value: TerrainPhysicsMaterialType.SOIL, label: t('terrain.physics.materials.soil') },
    { value: TerrainPhysicsMaterialType.SAND, label: t('terrain.physics.materials.sand') },
    { value: TerrainPhysicsMaterialType.ICE, label: t('terrain.physics.materials.ice') },
    { value: TerrainPhysicsMaterialType.WOOD, label: t('terrain.physics.materials.wood') },
    { value: TerrainPhysicsMaterialType.METAL, label: t('terrain.physics.materials.metal') },
    { value: TerrainPhysicsMaterialType.CUSTOM, label: t('terrain.physics.materials.custom') }
  ], [t]);

  return (
    <div className="terrain-physics-settings-tool">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title={t('terrain.physics.settings')} className="physics-settings-card">
            <Form layout="vertical">
              <Form.Item label={t('terrain.physics.usePhysics')}>
                <Switch
                  checked={usePhysics}
                  onChange={handleUsePhysicsChange}
                  disabled={!editable}
                />
                <Text style={{ marginLeft: 8 }}>{t('terrain.physics.enablePhysics')}</Text>
              </Form.Item>

              {usePhysics && (
                <>
                  <Form.Item
                    label={t('terrain.physics.physicsResolution')}
                    tooltip={t('terrain.physics.physicsResolutionTooltip')}
                  >
                    <Slider
                      min={16}
                      max={256}
                      step={16}
                      value={physicsResolution}
                      onChange={handlePhysicsResolutionChange}
                      disabled={!editable}
                      marks={{
                        16: '16',
                        64: '64',
                        128: '128',
                        256: '256'
                      }}
                    />
                  </Form.Item>

                  <Divider>{t('terrain.physics.materialSettings')}</Divider>

                  <Form.Item label={t('terrain.physics.materialType')}>
                    <Select
                      value={materialType}
                      onChange={handleMaterialTypeChange}
                      disabled={!editable}
                      style={{ width: '100%' }}
                    >
                      {materialTypeOptions.map(option => (
                        <Option key={option.value} value={option.value}>{option.label}</Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    label={t('terrain.physics.friction')}
                    tooltip={t('terrain.physics.frictionTooltip')}
                  >
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      value={friction}
                      onChange={handleFrictionChange}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item
                    label={t('terrain.physics.restitution')}
                    tooltip={t('terrain.physics.restitutionTooltip')}
                  >
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      value={restitution}
                      onChange={handleRestitutionChange}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Form.Item
                    label={t('terrain.physics.density')}
                    tooltip={t('terrain.physics.densityTooltip')}
                  >
                    <Slider
                      min={0.1}
                      max={10}
                      step={0.1}
                      value={density}
                      onChange={handleDensityChange}
                      disabled={!editable}
                    />
                  </Form.Item>

                  <Divider>{t('terrain.physics.debugSettings')}</Divider>

                  <Form.Item label={t('terrain.physics.showDebugVisuals')}>
                    <Switch
                      checked={showDebugVisuals}
                      onChange={handleShowDebugVisualsChange}
                      disabled={!editable}
                    />
                    <Text style={{ marginLeft: 8 }}>{t('terrain.physics.enableDebugVisuals')}</Text>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      onClick={handleApplySettings}
                      disabled={!editable}
                    >
                      {t('terrain.physics.applySettings')}
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TerrainPhysicsSettingsTool;
